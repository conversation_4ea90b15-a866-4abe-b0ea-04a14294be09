import React from 'react';
import { useApp } from '../../context/AppContext';
import styles from './AppInfoGrid.module.css';

function AppInfoGrid() {
  const { state } = useApp();
  const { appInfo } = state;

  const infoItems = [
    { label: 'App Version', value: appInfo.version },
    { label: 'Platform', value: appInfo.platform },
    { label: 'Electron Version', value: appInfo.electronVersion },
    { label: 'Node.js Version', value: appInfo.nodeVersion }
  ];

  return (
    <div className={styles.infoGrid}>
      {infoItems.map((item, index) => (
        <div key={index} className={styles.infoItem}>
          <h3 className={styles.label}>{item.label}</h3>
          <p className={styles.value}>{item.value}</p>
        </div>
      ))}
    </div>
  );
}

export default AppInfoGrid;
