import React, { useState } from 'react';
import { useApp } from '../../context/AppContext';
import AppInfoGrid from '../AppInfoGrid/AppInfoGrid';
import ActionButtons from '../ActionButtons/ActionButtons';
import InfoModal from '../InfoModal/InfoModal';
import styles from './WelcomeCard.module.css';

function WelcomeCard() {
  const [showInfoModal, setShowInfoModal] = useState(false);
  
  const handleShowInfo = () => {
    setShowInfoModal(true);
  };

  const handleCloseInfo = () => {
    setShowInfoModal(false);
  };

  return (
    <>
      <div className={styles.welcomeCard}>
        <h2 className={styles.title}>Welcome to Your Electron React App!</h2>
        <p className={styles.description}>
          This is a modern Electron application built with React, security best practices, 
          and a clean architecture. The app demonstrates proper IPC communication, 
          context isolation, and modern UI design patterns.
        </p>
        
        <AppInfoGrid />
        <ActionButtons onShowInfo={handleShowInfo} />
      </div>
      
      {showInfoModal && <InfoModal onClose={handleCloseInfo} />}
    </>
  );
}

export default WelcomeCard;
