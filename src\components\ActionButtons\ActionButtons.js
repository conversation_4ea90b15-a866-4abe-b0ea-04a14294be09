import React from 'react';
import { useApp } from '../../context/AppContext';
import styles from './ActionButtons.module.css';

function ActionButtons({ onShowInfo }) {
  const { state, actions } = useApp();
  const { isLoading } = state;

  const handleAsyncOperation = async () => {
    try {
      await actions.performAsyncOperation();
    } catch (error) {
      // Error is already handled in the context
      console.error('Async operation failed:', error);
    }
  };

  return (
    <div className={styles.buttons}>
      <button 
        className={styles.btn}
        onClick={handleAsyncOperation}
        disabled={isLoading}
      >
        {isLoading ? 'Processing...' : 'Test Async Operation'}
      </button>
      <button 
        className={`${styles.btn} ${styles.secondary}`}
        onClick={onShowInfo}
        disabled={isLoading}
      >
        Show App Info
      </button>
    </div>
  );
}

export default ActionButtons;
