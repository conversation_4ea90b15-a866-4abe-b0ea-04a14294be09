import React, { useEffect } from 'react';
import { useApp } from '../../context/AppContext';
import styles from './InfoModal.module.css';

function InfoModal({ onClose }) {
  const { state } = useApp();
  const { appInfo } = state;

  // Close modal on Escape key
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  const info = {
    'App Name': 'Electron React App',
    'Version': appInfo.version,
    'Platform': appInfo.platform,
    'Electron': appInfo.electronVersion,
    'Node.js': appInfo.nodeVersion,
    'Chrome': process.versions?.chrome || 'N/A',
    'V8': process.versions?.v8 || 'N/A'
  };

  let infoText = 'Application Information:\n\n';
  for (const [key, value] of Object.entries(info)) {
    infoText += `${key}: ${value}\n`;
  }

  const handleBackgroundClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className={styles.modal} onClick={handleBackgroundClick}>
      <div className={styles.modalContent}>
        <pre className={styles.infoText}>{infoText}</pre>
        <button className={styles.closeBtn} onClick={onClose}>
          Close
        </button>
      </div>
    </div>
  );
}

export default InfoModal;
