import React from 'react';
import { useApp } from '../../context/AppContext';
import styles from './StatusMessage.module.css';

function StatusMessage() {
  const { state, actions } = useApp();
  const { status } = state;

  if (!status.visible) {
    return null;
  }

  const handleClose = () => {
    actions.hideStatus();
  };

  return (
    <div className={`${styles.status} ${styles[status.type]} ${styles.show}`}>
      <span className={styles.message}>{status.message}</span>
      {status.type === 'error' && (
        <button className={styles.closeBtn} onClick={handleClose}>
          ×
        </button>
      )}
    </div>
  );
}

export default StatusMessage;
