{"name": "electron-app", "version": "1.0.0", "description": "A modern Electron application built with React and best practices", "main": "main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:renderer\" \"wait-on http://localhost:3000 && electron .\"", "dev:renderer": "webpack serve --mode development", "build": "webpack --mode production", "build:electron": "npm run build && electron-builder", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .js,.jsx", "clean": "rm -rf node_modules package-lock.json && npm install"}, "keywords": ["electron", "desktop", "app", "react"], "author": "Planfuly Inc.", "type": "commonjs", "dependencies": {"core-js": "^3.45.1", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@babel/cli": "^7.28.3", "@babel/core": "^7.26.0", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "babel-loader": "^9.2.1", "concurrently": "^9.1.0", "css-loader": "^7.1.2", "electron": "^38.0.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.9.2", "style-loader": "^4.0.0", "url-loader": "^4.1.1", "wait-on": "^8.0.1", "webpack": "^5.97.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "moduleNameMapping": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}}}